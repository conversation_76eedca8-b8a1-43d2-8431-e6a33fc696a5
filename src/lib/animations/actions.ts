import type { Action } from "svelte/action"
import { animate, inView, scroll } from "@motionone/dom"
import { browser } from "$app/environment"
import { prefersReducedMotion, ANIMATION_CONFIG } from "./index"

// Fade in action
export const fadeIn: Action<
  HTMLElement,
  {
    delay?: number
    duration?: number
    distance?: number
    direction?: "up" | "down" | "left" | "right"
    threshold?: number
  }
> = (node, options = {}) => {
  if (!browser || prefersReducedMotion()) return {}

  const {
    delay = 0,
    duration = ANIMATION_CONFIG.duration.normal,
    distance = 20,
    direction = "up",
    threshold = 0.1,
  } = options

  // Calculate transform based on direction
  const getTransform = (dist: number) => {
    switch (direction) {
      case "up":
        return `translateY(${dist}px)`
      case "down":
        return `translateY(${-dist}px)`
      case "left":
        return `translateX(${dist}px)`
      case "right":
        return `translateX(${-dist}px)`
      default:
        return `translateY(${dist}px)`
    }
  }

  // Set initial state
  animate(
    node,
    {
      opacity: 0,
      transform: getTransform(distance),
    },
    { duration: 0 },
  )

  // Animate in when in view
  const cleanup = inView(
    node,
    () => {
      animate(
        node,
        {
          opacity: 1,
          transform: "translate(0px, 0px)",
        },
        {
          duration,
          delay,
          easing: ANIMATION_CONFIG.easing.easeOut,
        },
      )
    },
    { margin: `0px 0px -${threshold * 100}% 0px` },
  )

  return {
    destroy: cleanup,
  }
}

// Parallax scroll action
export const parallax: Action<
  HTMLElement,
  {
    speed?: number
    direction?: "up" | "down"
    offset?: [string, string]
  }
> = (node, options = {}) => {
  if (!browser || prefersReducedMotion()) return {}

  const {
    speed = 0.5,
    direction = "up",
    offset = ["start end", "end start"],
  } = options
  const multiplier = direction === "up" ? -speed : speed

  const cleanup = scroll(
    animate(node, {
      transform: [
        `translateY(${-50 * multiplier}px)`,
        `translateY(${50 * multiplier}px)`,
      ],
    }),
    {
      target: node,
      offset,
    },
  )

  return {
    destroy: cleanup,
  }
}

// Scale on scroll action
export const scaleScroll: Action<
  HTMLElement,
  {
    from?: number
    to?: number
    offset?: [string, string]
  }
> = (node, options = {}) => {
  if (!browser || prefersReducedMotion()) return {}

  const { from = 0.8, to = 1, offset = ["start end", "end start"] } = options

  const cleanup = scroll(
    animate(node, {
      transform: [`scale(${from})`, `scale(${to})`],
    }),
    {
      target: node,
      offset,
    },
  )

  return {
    destroy: cleanup,
  }
}

// Hover scale action
export const hoverScale: Action<
  HTMLElement,
  {
    scale?: number
    duration?: number
  }
> = (node, options = {}) => {
  if (!browser || prefersReducedMotion()) return {}

  const { scale = 1.05, duration = ANIMATION_CONFIG.duration.fast } = options
  let hoverAnimation: any = null

  const handleMouseEnter = () => {
    if (hoverAnimation) hoverAnimation.stop()
    hoverAnimation = animate(
      node,
      { transform: `scale(${scale})` },
      {
        duration,
        easing: ANIMATION_CONFIG.easing.easeOut,
      },
    )
  }

  const handleMouseLeave = () => {
    if (hoverAnimation) hoverAnimation.stop()
    hoverAnimation = animate(
      node,
      { transform: "scale(1)" },
      {
        duration,
        easing: ANIMATION_CONFIG.easing.easeOut,
      },
    )
  }

  node.addEventListener("mouseenter", handleMouseEnter)
  node.addEventListener("mouseleave", handleMouseLeave)

  return {
    destroy() {
      node.removeEventListener("mouseenter", handleMouseEnter)
      node.removeEventListener("mouseleave", handleMouseLeave)
      if (hoverAnimation) hoverAnimation.stop()
    },
  }
}

// Stagger children action
export const staggerChildren: Action<
  HTMLElement,
  {
    selector?: string
    delay?: number
    stagger?: number
    duration?: number
    distance?: number
  }
> = (node, options = {}) => {
  if (!browser || prefersReducedMotion()) return {}

  const {
    selector = "> *",
    delay = 0,
    stagger = ANIMATION_CONFIG.stagger.normal,
    duration = ANIMATION_CONFIG.duration.normal,
    distance = 20,
  } = options

  const children = node.querySelectorAll(selector)
  const cleanupFunctions: (() => void)[] = []

  children.forEach((child, index) => {
    // Set initial state
    animate(
      child,
      {
        opacity: 0,
        transform: `translateY(${distance}px)`,
      },
      { duration: 0 },
    )

    // Animate in when parent is in view
    const cleanup = inView(
      node,
      () => {
        animate(
          child,
          {
            opacity: 1,
            transform: "translateY(0px)",
          },
          {
            duration,
            delay: delay + index * stagger,
            easing: ANIMATION_CONFIG.easing.easeOut,
          },
        )
      },
      { margin: "0px 0px -10% 0px" },
    )

    cleanupFunctions.push(cleanup)
  })

  return {
    destroy() {
      cleanupFunctions.forEach((cleanup) => cleanup())
    },
  }
}

// Rotate on scroll action
export const rotateScroll: Action<
  HTMLElement,
  {
    from?: number
    to?: number
    offset?: [string, string]
  }
> = (node, options = {}) => {
  if (!browser || prefersReducedMotion()) return {}

  const { from = 0, to = 360, offset = ["start end", "end start"] } = options

  const cleanup = scroll(
    animate(node, {
      transform: [`rotate(${from}deg)`, `rotate(${to}deg)`],
    }),
    {
      target: node,
      offset,
    },
  )

  return {
    destroy: cleanup,
  }
}

// Opacity on scroll action
export const opacityScroll: Action<
  HTMLElement,
  {
    from?: number
    to?: number
    offset?: [string, string]
  }
> = (node, options = {}) => {
  if (!browser || prefersReducedMotion()) return {}

  const { from = 1, to = 0, offset = ["start start", "end start"] } = options

  const cleanup = scroll(
    animate(node, {
      opacity: [from, to],
    }),
    {
      target: node,
      offset,
    },
  )

  return {
    destroy: cleanup,
  }
}

// Button click feedback action
export const clickFeedback: Action<HTMLElement> = (node) => {
  if (!browser || prefersReducedMotion()) return {}

  const handleClick = (event: MouseEvent) => {
    // Create ripple effect
    const rect = node.getBoundingClientRect()
    const ripple = document.createElement("div")

    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    ripple.style.position = "absolute"
    ripple.style.borderRadius = "50%"
    ripple.style.background = "rgba(255, 255, 255, 0.3)"
    ripple.style.pointerEvents = "none"
    ripple.style.width = "20px"
    ripple.style.height = "20px"
    ripple.style.left = `${x}px`
    ripple.style.top = `${y}px`
    ripple.style.transform = "translate(-50%, -50%)"
    ripple.style.zIndex = "1000"

    node.style.position = "relative"
    node.style.overflow = "hidden"
    node.appendChild(ripple)

    // Animate ripple
    animate(
      ripple,
      {
        transform: [
          "translate(-50%, -50%) scale(0)",
          "translate(-50%, -50%) scale(4)",
        ],
        opacity: [0.6, 0],
      },
      {
        duration: 0.6,
        easing: ANIMATION_CONFIG.easing.easeOut,
      },
    ).finished.then(() => {
      ripple.remove()
    })

    // Button scale feedback
    animate(
      node,
      {
        transform: ["scale(1)", "scale(0.95)", "scale(1)"],
      },
      {
        duration: 0.2,
        easing: ANIMATION_CONFIG.easing.easeOut,
      },
    )
  }

  node.addEventListener("click", handleClick)

  return {
    destroy() {
      node.removeEventListener("click", handleClick)
    },
  }
}

// Input focus animation action
export const focusAnimation: Action<HTMLInputElement | HTMLTextAreaElement> = (
  node,
) => {
  if (!browser || prefersReducedMotion()) return {}

  const handleFocus = () => {
    animate(
      node,
      {
        transform: "scale(1.02)",
        boxShadow: "0 0 0 2px rgba(59, 130, 246, 0.3)",
      },
      {
        duration: ANIMATION_CONFIG.duration.fast,
        easing: ANIMATION_CONFIG.easing.easeOut,
      },
    )
  }

  const handleBlur = () => {
    animate(
      node,
      {
        transform: "scale(1)",
        boxShadow: "0 0 0 0px rgba(59, 130, 246, 0)",
      },
      {
        duration: ANIMATION_CONFIG.duration.fast,
        easing: ANIMATION_CONFIG.easing.easeOut,
      },
    )
  }

  node.addEventListener("focus", handleFocus)
  node.addEventListener("blur", handleBlur)

  return {
    destroy() {
      node.removeEventListener("focus", handleFocus)
      node.removeEventListener("blur", handleBlur)
    },
  }
}

// Card hover animation action
export const cardHover: Action<HTMLElement> = (node) => {
  if (!browser || prefersReducedMotion()) return {}

  let hoverAnimation: any = null

  const handleMouseEnter = () => {
    if (hoverAnimation) hoverAnimation.stop()
    hoverAnimation = animate(
      node,
      {
        transform: "translateY(-4px)",
        boxShadow: "0 10px 25px rgba(0, 0, 0, 0.15)",
      },
      {
        duration: ANIMATION_CONFIG.duration.normal,
        easing: ANIMATION_CONFIG.easing.easeOut,
      },
    )
  }

  const handleMouseLeave = () => {
    if (hoverAnimation) hoverAnimation.stop()
    hoverAnimation = animate(
      node,
      {
        transform: "translateY(0px)",
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
      },
      {
        duration: ANIMATION_CONFIG.duration.normal,
        easing: ANIMATION_CONFIG.easing.easeOut,
      },
    )
  }

  node.addEventListener("mouseenter", handleMouseEnter)
  node.addEventListener("mouseleave", handleMouseLeave)

  return {
    destroy() {
      node.removeEventListener("mouseenter", handleMouseEnter)
      node.removeEventListener("mouseleave", handleMouseLeave)
      if (hoverAnimation) hoverAnimation.stop()
    },
  }
}
