<script lang="ts">
  import { fade } from 'svelte/transition';
  import { CheckCircle2, XCircle, Eye, EyeOff } from 'lucide-svelte';
  
  export let password: string = '';
  export let confirmPassword: string = '';
  export let showPassword: boolean = false;
  
  $: hasInput = confirmPassword.length > 0;
  $: isMatching = password === confirmPassword && hasInput;
  $: isPartialMatch = password.startsWith(confirmPassword) && hasInput && !isMatching;
  
  // Calculate how many characters match from the beginning
  $: matchingChars = (() => {
    let count = 0;
    for (let i = 0; i < Math.min(password.length, confirmPassword.length); i++) {
      if (password[i] === confirmPassword[i]) {
        count++;
      } else {
        break;
      }
    }
    return count;
  })();
  
  $: matchPercentage = password.length > 0 ? (matchingChars / password.length) * 100 : 0;
</script>

{#if hasInput}
  <div transition:fade={{ duration: 200 }} class="space-y-2">
    <!-- Match status -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2 text-sm">
        {#if isMatching}
          <CheckCircle2 size={16} class="text-success" />
          <span class="text-success font-medium">Passwords match!</span>
        {:else if isPartialMatch}
          <div class="flex items-center gap-2 text-warning">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10" />
              <path d="M12 6v6l4 2" />
            </svg>
            <span>Keep typing...</span>
          </div>
        {:else}
          <XCircle size={16} class="text-error" />
          <span class="text-error">Passwords don't match</span>
        {/if}
      </div>
      
      <!-- Toggle visibility hint -->
      {#if !isMatching && confirmPassword.length > 3}
        <button
          type="button"
          on:click={() => showPassword = !showPassword}
          class="flex items-center gap-1 text-xs text-base-content/60 hover:text-base-content/80 transition-colors"
        >
          {#if showPassword}
            <EyeOff size={14} />
            <span>Hide</span>
          {:else}
            <Eye size={14} />
            <span>Show</span>
          {/if}
        </button>
      {/if}
    </div>
    
    <!-- Visual match indicator -->
    {#if !isMatching && confirmPassword.length > 0}
      <div class="space-y-1">
        <div class="flex justify-between text-xs text-base-content/60">
          <span>Match progress</span>
          <span>{matchingChars}/{password.length} characters</span>
        </div>
        <div class="relative h-1.5 bg-base-300 rounded-full overflow-hidden">
          <div 
            class="absolute inset-y-0 left-0 bg-warning rounded-full transition-all duration-300"
            style="width: {matchPercentage}%"
          ></div>
        </div>
      </div>
    {/if}
    
    <!-- Character difference indicator for debugging -->
    {#if !isMatching && confirmPassword.length > password.length}
      <p class="text-xs text-base-content/60">
        Confirmation is {confirmPassword.length - password.length} character{confirmPassword.length - password.length !== 1 ? 's' : ''} longer
      </p>
    {/if}
  </div>
{/if}